2025-07-01 21:10:25,097 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-01 21:10:25,098 - autogen_framework.main - INFO - Environment: development
2025-07-01 21:10:25,098 - autogen_framework.main - INFO - Debug mode: True
2025-07-01 21:10:25,098 - autogen_framework.main - INFO - Application startup complete
2025-07-01 21:10:42,657 - autogen_framework.main - INFO - Shutting down application
2025-07-01 21:10:42,657 - autogen_framework.main - INFO - Application shutdown complete
2025-07-01 21:35:48,029 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-01 21:35:48,029 - autogen_framework.main - INFO - Environment: development
2025-07-01 21:35:48,029 - autogen_framework.main - INFO - Debug mode: True
2025-07-01 21:35:48,029 - autogen_framework.main - INFO - Application startup complete
2025-07-01 21:36:20,929 - autogen_framework.main - INFO - Shutting down application
2025-07-01 21:36:20,929 - autogen_framework.main - INFO - Application shutdown complete
2025-07-01 23:13:45,312 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-01 23:13:45,312 - autogen_framework.main - INFO - Environment: development
2025-07-01 23:13:45,312 - autogen_framework.main - INFO - Debug mode: True
2025-07-01 23:13:45,312 - autogen_framework.main - INFO - Application startup complete
2025-07-01 23:13:47,219 - autogen_framework.main - INFO - Shutting down application
2025-07-01 23:13:47,219 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 00:41:37,274 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 00:41:37,275 - autogen_framework.main - INFO - Environment: development
2025-07-02 00:41:37,275 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 00:41:37,275 - autogen_framework.main - INFO - Application startup complete
2025-07-02 00:41:39,806 - autogen_framework.main - INFO - Shutting down application
2025-07-02 00:41:39,806 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 01:24:07,997 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:24:07,998 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:24:07,998 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:24:07,999 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:26:27,375 - autogen_framework.main - INFO - Shutting down application
2025-07-02 01:26:27,375 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 01:26:55,831 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:26:55,832 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:26:55,832 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:26:55,833 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:27:39,159 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:27:39,258 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:27:39,262 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:27:39,375 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:27:41,631 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:31:05,604 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:31:05,605 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:31:05,605 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:31:05,605 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:31:45,662 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:33:16,494 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:33:16,502 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:35:44,503 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:36:20,178 - autogen_framework.main - INFO - Shutting down application
2025-07-02 01:36:20,179 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 01:36:22,345 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:36:22,345 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:36:22,346 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:36:22,346 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:36:32,646 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:36:32,685 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:37:00,364 - autogen_framework.main - INFO - Shutting down application
2025-07-02 01:37:00,364 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 01:37:05,857 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:37:05,857 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:37:05,857 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:37:05,858 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:37:12,274 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:37:12,304 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:37:13,344 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:29:29,275 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:29:29,279 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:29:34,229 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:29:34,229 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:29:34,230 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:29:34,230 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:29:49,161 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:29:49,162 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:29:51,809 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:29:51,810 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:29:51,810 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:29:51,810 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:30:37,678 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:30:37,678 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:30:39,760 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:30:39,760 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:30:39,760 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:30:39,760 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:31:40,392 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:31:40,393 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:31:42,171 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:31:42,171 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:31:42,172 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:31:42,172 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:33:00,278 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:33:00,278 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:33:02,600 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:33:02,600 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:33:02,601 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:33:02,601 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:33:46,849 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:33:46,850 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:33:49,032 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:33:49,032 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:33:49,032 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:33:49,032 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:34:18,246 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:34:18,246 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:34:20,486 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:34:20,486 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:34:20,486 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:34:20,487 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:34:40,754 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:34:40,755 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:34:42,490 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:34:42,491 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:34:42,491 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:34:42,491 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:34:57,537 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:34:57,537 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:34:59,254 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:34:59,255 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:34:59,255 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:34:59,255 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:35:24,678 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:35:24,679 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:35:26,696 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:35:26,697 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:35:26,697 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:35:26,698 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:36:42,997 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:36:42,998 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:36:45,731 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:36:45,731 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:36:45,731 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:36:45,731 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:47:08,113 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:47:08,114 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:47:10,482 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:47:10,482 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:47:10,482 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:47:10,482 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:47:41,413 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:47:41,414 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:47:43,105 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:47:43,105 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:47:43,105 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:47:43,105 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:48:14,827 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:48:14,828 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:48:17,317 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:48:17,318 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:48:17,318 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:48:17,318 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:49:03,480 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:49:03,480 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:49:05,409 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:49:05,410 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:49:05,410 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:49:05,411 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:49:34,810 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:49:34,810 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:49:36,783 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:49:36,783 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:49:36,783 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:49:36,783 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:50:24,885 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:50:24,885 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:50:26,706 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:50:26,706 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:50:26,707 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:50:26,707 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:51:52,385 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:51:52,385 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:51:54,179 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:51:54,179 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:51:54,179 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:51:54,179 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:12:24,149 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:12:24,150 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:12:25,904 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:12:25,905 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:12:25,905 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:12:25,906 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:13:01,510 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:13:01,510 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:13:03,895 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:13:03,896 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:13:03,896 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:13:03,896 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:13:44,627 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:13:44,628 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:13:46,438 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:13:46,438 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:13:46,439 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:13:46,439 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:14:10,053 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:14:10,054 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:14:11,711 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:14:11,712 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:14:11,712 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:14:11,712 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:14:42,387 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:14:42,388 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:14:44,555 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:14:44,555 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:14:44,555 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:14:44,555 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:15:10,922 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:15:10,923 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:15:12,662 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:15:12,662 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:15:12,663 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:15:12,663 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:36:28,645 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 03:36:28,670 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 03:36:29,453 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 09:52:13,077 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:13,077 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:16,767 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:16,768 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:16,768 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:16,768 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:52:26,841 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:26,841 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:29,526 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:29,526 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:29,527 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:29,527 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:52:41,755 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:41,756 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:43,566 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:43,566 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:43,566 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:43,566 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:52:55,550 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:55,550 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:57,423 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:57,423 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:57,423 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:57,423 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:53:15,711 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:53:15,711 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:53:17,705 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:53:17,705 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:53:17,705 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:53:17,706 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:53:47,757 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:53:47,757 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:53:49,380 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:53:49,380 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:53:49,380 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:53:49,382 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:53:59,554 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:53:59,554 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:54:01,473 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:54:01,473 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:54:01,473 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:54:01,473 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:54:25,421 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:54:25,422 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:54:27,816 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:54:27,816 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:54:27,816 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:54:27,816 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:54:40,046 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:54:40,047 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:54:41,850 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:54:41,850 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:54:41,850 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:54:41,850 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:00,705 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:00,705 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:02,441 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:02,442 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:02,442 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:02,442 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:16,763 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:16,763 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:18,440 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:18,440 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:18,440 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:18,440 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:32,302 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:32,302 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:34,234 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:34,234 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:34,234 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:34,234 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:46,545 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:46,546 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:48,044 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:48,044 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:48,044 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:48,045 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:56:03,052 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:56:03,052 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:56:05,205 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:56:05,206 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:56:05,206 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:56:05,206 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:56:20,047 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:56:20,047 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:56:22,009 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:56:22,009 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:56:22,009 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:56:22,009 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:56:57,700 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:56:57,700 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:56:59,597 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:56:59,598 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:56:59,598 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:56:59,598 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:57:35,520 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:57:35,520 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:57:37,775 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:57:37,775 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:57:37,776 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:57:37,776 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:57:58,834 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:57:58,835 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:58:00,281 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:58:00,282 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:58:00,282 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:58:00,282 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:59:25,519 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:59:25,519 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:59:28,081 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:59:28,081 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:59:28,081 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:59:28,081 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:12:07,831 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:07,831 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:10,108 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:10,109 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:10,109 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:10,109 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:12:24,609 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:24,610 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:26,376 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:26,377 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:26,377 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:26,377 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:12:38,211 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:38,211 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:39,857 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:39,858 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:39,858 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:39,858 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:12:49,137 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:49,137 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:50,442 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:50,443 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:50,443 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:50,443 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:09,511 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:09,511 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:11,632 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:11,633 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:11,633 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:11,633 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:29,811 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:29,811 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:32,071 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:32,072 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:32,072 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:32,073 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:42,513 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:42,513 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:44,452 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:44,453 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:44,453 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:44,453 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:52,219 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:52,220 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:53,716 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:53,716 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:53,716 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:53,716 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:14:12,359 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:14:12,359 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:14:14,106 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:14:14,107 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:14:14,107 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:14:14,107 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:14:30,042 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:14:30,042 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:14:32,002 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:14:32,002 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:14:32,002 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:14:32,002 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:14:51,561 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:14:51,562 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:14:53,450 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:14:53,451 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:14:53,451 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:14:53,451 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:15:05,554 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:15:05,554 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:15:07,715 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:15:07,715 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:15:07,715 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:15:07,715 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:15:30,891 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:15:30,891 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:15:32,453 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:15:32,454 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:15:32,454 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:15:32,454 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:16:10,891 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:16:10,892 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:16:12,807 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:16:12,808 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:16:12,808 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:16:12,808 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:17:24,816 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:17:24,817 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:17:27,576 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:17:27,577 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:17:27,577 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:17:27,577 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:22:48,913 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:22:48,928 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:22:53,875 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:22:53,878 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:22:53,878 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:22:53,878 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:23:12,348 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:23:12,348 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:23:14,836 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:23:14,836 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:23:14,836 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:23:14,836 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:23:49,187 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:23:49,187 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:23:51,846 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:23:51,848 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:23:51,849 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:23:51,849 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:24:42,033 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:24:42,034 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:24:44,256 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:24:44,256 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:24:44,256 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:24:44,256 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:25:38,042 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:25:38,043 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:25:40,637 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:25:40,637 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:25:40,637 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:25:40,638 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:25:55,715 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:25:55,716 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:25:58,602 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:25:58,605 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:25:58,606 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:25:58,606 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:26:27,375 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:26:27,375 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:26:29,132 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:26:29,132 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:26:29,132 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:26:29,132 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:26:41,559 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:26:41,559 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:26:43,393 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:26:43,393 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:26:43,393 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:26:43,394 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:27:02,715 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:27:02,716 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:27:05,663 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:27:05,665 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:27:05,669 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:27:05,669 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:28:41,645 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:28:41,646 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:28:44,311 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:28:44,311 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:28:44,311 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:28:44,311 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:34:52,668 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:34:52,669 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:34:55,463 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:34:55,463 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:34:55,463 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:34:55,464 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:35:13,330 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:35:13,331 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:35:17,072 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:35:17,073 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:35:17,073 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:35:17,073 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:35:48,049 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:35:48,049 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:35:49,554 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:35:49,554 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:35:49,554 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:35:49,554 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:36:04,376 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:36:04,376 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:36:06,084 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:36:06,085 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:36:06,085 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:36:06,085 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:36:24,728 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:36:24,728 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:36:26,227 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:36:26,227 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:36:26,227 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:36:26,227 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:36:49,513 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:36:49,514 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:36:50,889 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:36:50,889 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:36:50,889 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:36:50,889 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:37:39,967 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:37:39,968 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:37:41,870 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:37:41,871 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:37:41,871 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:37:41,871 - autogen_framework.main - INFO - Application startup complete
2025-07-02 11:39:08,846 - autogen_framework.main - INFO - Shutting down application
2025-07-02 11:39:08,846 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 11:39:10,351 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 11:39:10,352 - autogen_framework.main - INFO - Environment: development
2025-07-02 11:39:10,352 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 11:39:10,352 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:06:41,224 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:06:41,224 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:06:42,797 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:06:42,797 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:06:42,797 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:06:42,798 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:23:35,819 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:23:35,819 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:23:37,710 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:23:37,711 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:23:37,711 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:23:37,711 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:24:20,563 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:24:20,563 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:24:22,156 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:24:22,157 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:24:22,157 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:24:22,157 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:24:34,930 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:24:34,931 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:24:36,316 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:24:36,316 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:24:36,316 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:24:36,317 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:25:01,934 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:25:01,934 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:25:03,948 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:25:03,948 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:25:03,949 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:25:03,949 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:25:23,848 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:25:23,848 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:25:25,426 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:25:25,426 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:25:25,427 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:25:25,427 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:29:34,019 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:29:34,019 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:29:35,529 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:29:35,529 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:29:35,529 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:29:35,529 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:29:40,562 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:29:40,563 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:29:42,060 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:29:42,060 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:29:42,060 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:29:42,060 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:30:36,760 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:30:36,761 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:33:06,328 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:33:06,328 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:33:06,328 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:33:06,328 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:36:01,485 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:36:01,485 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:36:02,976 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:36:02,977 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:36:02,977 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:36:02,977 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:37:08,477 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:37:08,477 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:37:09,634 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:37:09,634 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:37:09,634 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:37:09,634 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:54:55,104 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:54:55,105 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:54:57,059 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:54:57,060 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:54:57,060 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:54:57,060 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:55:29,358 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:55:29,358 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:55:30,971 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:55:30,971 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:55:30,971 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:55:30,972 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:56:32,229 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:56:32,229 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:56:34,659 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:56:34,659 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:56:34,659 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:56:34,659 - autogen_framework.main - INFO - Application startup complete
2025-07-02 14:56:35,264 - autogen_framework.main - INFO - Shutting down application
2025-07-02 14:56:35,264 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 14:56:36,621 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 14:56:36,621 - autogen_framework.main - INFO - Environment: development
2025-07-02 14:56:36,621 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 14:56:36,621 - autogen_framework.main - INFO - Application startup complete
2025-07-02 15:03:39,050 - autogen_framework.main - INFO - Shutting down application
2025-07-02 15:03:39,050 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 15:03:40,570 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 15:03:40,570 - autogen_framework.main - INFO - Environment: development
2025-07-02 15:03:40,570 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 15:03:40,570 - autogen_framework.main - INFO - Application startup complete
2025-07-02 15:12:56,839 - autogen_framework.main - INFO - Shutting down application
2025-07-02 15:12:56,840 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 15:12:58,455 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 15:12:58,456 - autogen_framework.main - INFO - Environment: development
2025-07-02 15:12:58,456 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 15:12:58,456 - autogen_framework.main - INFO - Application startup complete
2025-07-02 15:18:20,054 - autogen_framework.main - INFO - Shutting down application
2025-07-02 15:18:20,055 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 15:18:21,385 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 15:18:21,385 - autogen_framework.main - INFO - Environment: development
2025-07-02 15:18:21,386 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 15:18:21,386 - autogen_framework.main - INFO - Application startup complete
2025-07-02 17:47:13,414 - autogen_framework.main - INFO - Shutting down application
2025-07-02 17:47:13,415 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 17:47:15,118 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 17:47:15,118 - autogen_framework.main - INFO - Environment: development
2025-07-02 17:47:15,118 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 17:47:15,118 - autogen_framework.main - INFO - Application startup complete
2025-07-02 17:59:27,709 - autogen_framework.main - INFO - Shutting down application
2025-07-02 17:59:27,709 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 17:59:31,514 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 17:59:31,515 - autogen_framework.main - INFO - Environment: development
2025-07-02 17:59:31,515 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 17:59:31,515 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:00:07,069 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:00:07,069 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:00:11,941 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:00:11,941 - autogen_framework.main - INFO - Environment: prodect
2025-07-02 18:00:11,941 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:00:11,942 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:00:20,715 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:00:20,716 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:00:26,389 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:00:26,390 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:00:26,390 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:00:26,390 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:01:18,852 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:01:18,852 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:01:22,476 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:01:22,476 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:01:22,477 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:01:22,477 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:01:54,247 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:01:54,247 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:01:55,830 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:01:55,831 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:01:55,831 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:01:55,831 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:02:04,724 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:02:04,724 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:02:12,312 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:02:12,312 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:02:12,312 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:02:12,313 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:02:29,089 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:02:29,089 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:02:29,090 - autogen_framework.main - INFO - Debug mode: False
2025-07-02 18:02:29,090 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:02:34,829 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:02:34,829 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:02:38,053 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:02:38,054 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:02:38,054 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:02:38,054 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:04:55,861 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:04:55,862 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:04:57,139 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:04:57,139 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:04:57,140 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:04:57,140 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:05:50,832 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:05:50,832 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:05:52,185 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:05:52,185 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:05:52,186 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:05:52,186 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:06:02,068 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:06:02,069 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:06:03,734 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:06:03,735 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:06:03,735 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:06:03,735 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:07:47,960 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:07:47,960 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:07:49,130 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:07:49,131 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:07:49,131 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:07:49,131 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:18:13,741 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:18:13,742 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:18:15,174 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:18:15,175 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:18:15,175 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:18:15,175 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:38:29,442 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:38:29,443 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:38:30,581 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:38:30,582 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:38:30,582 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:38:30,582 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:38:37,908 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:38:37,908 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:38:38,987 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:38:38,987 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:38:38,987 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:38:38,987 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:47:09,600 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:47:09,600 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:47:10,603 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:47:10,604 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:47:10,604 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:47:10,604 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:50:31,957 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:50:31,957 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 18:50:33,512 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 18:50:33,513 - autogen_framework.main - INFO - Environment: development
2025-07-02 18:50:33,513 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 18:50:33,513 - autogen_framework.main - INFO - Application startup complete
2025-07-02 18:50:35,836 - autogen_framework.main - INFO - Shutting down application
2025-07-02 18:50:35,836 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 19:11:47,191 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 19:11:47,191 - autogen_framework.main - INFO - Environment: development
2025-07-02 19:11:47,191 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 19:11:47,191 - autogen_framework.main - INFO - Application startup complete
2025-07-02 19:11:58,831 - autogen_framework.main - INFO - Shutting down application
2025-07-02 19:11:58,832 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 20:59:54,235 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 20:59:54,235 - autogen_framework.main - INFO - Environment: development
2025-07-02 20:59:54,235 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 20:59:54,236 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:00:02,884 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 21:01:54,109 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:01:54,109 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:01:55,083 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:01:55,083 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:01:55,083 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:01:55,083 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:02:01,825 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:02:01,825 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:02:03,038 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:02:03,038 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:02:03,038 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:02:03,038 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:02:03,642 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:02:03,642 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:02:04,696 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:02:04,696 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:02:04,696 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:02:04,696 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:02:08,005 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:02:08,006 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:02:09,029 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:02:09,029 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:02:09,030 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:02:09,030 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:02:21,892 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:02:21,892 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:02:23,033 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:02:23,034 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:02:23,034 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:02:23,034 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:03:06,247 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 21:03:06,255 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 21:03:06,270 - autogen_framework.model_manager - INFO - Loaded 7 model configurations
2025-07-02 21:03:07,332 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 21:05:58,308 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:05:58,309 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:05:59,733 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:05:59,733 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:05:59,733 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:05:59,734 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:06:01,243 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:06:01,243 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:06:02,516 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:06:02,517 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:06:02,517 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:06:02,518 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:06:04,341 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:06:04,342 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:06:05,957 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:06:05,957 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:06:05,958 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:06:05,958 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:07:31,192 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:07:31,193 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:07:32,733 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:07:32,733 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:07:32,734 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:07:32,734 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:07:52,613 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:07:52,614 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:07:53,659 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:07:53,659 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:07:53,659 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:07:53,659 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:08:02,009 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:08:02,009 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:08:03,167 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:08:03,167 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:08:03,167 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:08:03,167 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:08:36,524 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:08:36,525 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:08:39,508 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:08:39,508 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:08:39,508 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:08:39,508 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:09:21,199 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:09:21,200 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:17:32,753 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:17:32,753 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:17:32,754 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:17:32,754 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:18:35,841 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:18:35,841 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:18:37,104 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:18:37,104 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:18:37,104 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:18:37,104 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:19:09,626 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:19:09,627 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:19:10,963 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:19:10,963 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:19:10,963 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:19:10,963 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:19:16,286 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:19:16,287 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:19:17,424 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:19:17,424 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:19:17,424 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:19:17,425 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:19:30,384 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:19:30,384 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:19:31,449 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:19:31,450 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:19:31,450 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:19:31,450 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:19:34,480 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:19:34,481 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:19:35,592 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:19:35,592 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:19:35,592 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:19:35,593 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:20:34,713 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:20:34,713 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:20:35,957 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:20:35,957 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:20:35,957 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:20:35,957 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:21:47,495 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:21:47,495 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:21:48,938 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:21:48,939 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:21:48,939 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:21:48,939 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:22:00,134 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:22:00,134 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:22:01,283 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:22:01,283 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:22:01,283 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:22:01,283 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:22:07,908 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:22:07,908 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:22:08,974 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:22:08,974 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:22:08,974 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:22:08,974 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:22:40,307 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:22:40,307 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:22:41,412 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:22:41,413 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:22:41,413 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:22:41,413 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:22:50,548 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:22:50,549 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:22:51,562 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:22:51,562 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:22:51,562 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:22:51,562 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:23:49,500 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:23:49,500 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:23:50,674 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:23:50,675 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:23:50,675 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:23:50,675 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:24:08,644 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:24:08,644 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:24:09,893 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:24:09,894 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:24:09,894 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:24:09,894 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:24:48,879 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:24:48,880 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:24:50,069 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:24:50,069 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:24:50,069 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:24:50,070 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:25:17,468 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:25:17,468 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:25:18,566 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:25:18,566 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:25:18,566 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:25:18,566 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:26:07,678 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:26:07,678 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:26:08,837 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:26:08,838 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:26:08,838 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:26:08,838 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:26:14,072 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:26:14,072 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:26:15,164 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:26:15,164 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:26:15,165 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:26:15,165 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:26:23,600 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:26:23,600 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:26:24,692 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:26:24,692 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:26:24,692 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:26:24,692 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:26:38,468 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:26:38,468 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:26:39,565 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:26:39,565 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:26:39,565 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:26:39,565 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:27:01,289 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:27:01,289 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:27:02,277 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:27:02,278 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:27:02,278 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:27:02,278 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:29:26,006 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:29:26,006 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:29:27,103 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:29:27,103 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:29:27,103 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:29:27,103 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:30:15,675 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:30:15,675 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:30:16,881 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:30:16,881 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:30:16,881 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:30:16,881 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:31:12,536 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:31:12,536 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:31:13,706 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:31:13,707 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:31:13,707 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:31:13,707 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:31:28,247 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:31:28,247 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:31:29,482 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:31:29,482 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:31:29,482 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:31:29,482 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:32:27,111 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:32:27,112 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:32:28,298 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:32:28,299 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:32:28,299 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:32:28,299 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:32:32,613 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:32:32,614 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:32:33,738 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:32:33,738 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:32:33,738 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:32:33,739 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:33:02,656 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:33:02,656 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:33:03,818 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:33:03,819 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:33:03,819 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:33:03,819 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:34:01,554 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:34:01,554 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:34:02,729 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:34:02,730 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:34:02,730 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:34:02,730 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:37:44,587 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:37:44,587 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:37:45,561 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:37:45,561 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:37:45,561 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:37:45,561 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:37:47,869 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:37:47,869 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:37:48,910 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:37:48,910 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:37:48,910 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:37:48,910 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:38:28,793 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:38:28,793 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:38:30,029 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:38:30,029 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:38:30,029 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:38:30,029 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:38:31,133 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:38:31,133 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:38:32,178 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:38:32,178 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:38:32,178 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:38:32,178 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:38:58,298 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:38:58,299 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:38:59,332 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:38:59,332 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:38:59,332 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:38:59,332 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:39:49,233 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:39:49,234 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:39:50,356 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:39:50,356 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:39:50,356 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:39:50,356 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:40:29,034 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:40:29,034 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:40:30,399 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:40:30,399 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:40:30,399 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:40:30,399 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:40:31,103 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:40:31,103 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:40:32,284 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:40:32,284 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:40:32,284 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:40:32,284 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:40:46,644 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:40:46,645 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:40:48,280 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:40:48,280 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:40:48,280 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:40:48,280 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:40:49,889 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:40:49,890 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:40:51,212 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:40:51,212 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:40:51,212 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:40:51,212 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:40:54,643 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:40:54,643 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:40:55,850 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:40:55,850 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:40:55,850 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:40:55,850 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:42:33,175 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:42:33,176 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:42:34,356 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:42:34,358 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:42:34,358 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:42:34,362 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:42:53,843 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:42:53,844 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:42:55,024 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:42:55,025 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:42:55,025 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:42:55,025 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:43:00,447 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:43:00,448 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:43:01,562 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:43:01,562 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:43:01,562 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:43:01,563 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:44:21,288 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:44:21,288 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:44:22,355 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:44:22,355 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:44:22,355 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:44:22,355 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:46:22,090 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:46:22,090 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:46:23,354 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:46:23,355 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:46:23,355 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:46:23,355 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:46:30,820 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:46:30,820 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:46:32,163 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:46:32,163 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:46:32,163 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:46:32,164 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:46:49,036 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:46:49,036 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:46:50,468 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:46:50,468 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:46:50,469 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:46:50,469 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:52:30,524 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:52:30,524 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:52:33,815 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:52:33,815 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:52:33,815 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:52:33,815 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:56:33,663 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:56:33,663 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:56:35,109 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:56:35,109 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:56:35,109 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:56:35,109 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:56:41,488 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:56:41,489 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:56:43,501 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:56:43,502 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:56:43,502 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:56:43,502 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:57:45,779 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:57:45,779 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:57:46,887 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:57:46,888 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:57:46,888 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:57:46,888 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:58:07,310 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:58:07,310 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:58:08,846 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:58:08,846 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:58:08,846 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:58:08,846 - autogen_framework.main - INFO - Application startup complete
2025-07-02 21:59:03,535 - autogen_framework.main - INFO - Shutting down application
2025-07-02 21:59:03,535 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 21:59:04,707 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 21:59:04,707 - autogen_framework.main - INFO - Environment: development
2025-07-02 21:59:04,707 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 21:59:04,707 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:01:12,580 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:01:12,580 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:01:13,815 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:01:13,815 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:01:13,815 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:01:13,815 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:01:30,787 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:01:30,787 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:01:32,400 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:01:32,400 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:01:32,400 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:01:32,400 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:01:37,349 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:01:37,349 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:01:38,809 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:01:38,810 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:01:38,810 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:01:38,811 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:04:31,275 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:04:31,275 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:04:36,311 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:04:36,311 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:04:36,311 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:04:36,311 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:05:11,440 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:05:11,441 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:11:12,031 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:11:12,031 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:11:12,031 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:11:12,031 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:11:44,490 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:11:44,490 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:12:52,687 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:12:52,688 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:12:52,688 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:12:52,688 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:13:58,880 - autogen_core - INFO - Sending message of type GroupChatStart to RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688: {'messages': [TextMessage(id='cd424942-e212-4731-87a9-99c4adbaab7d', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 13, 58, 878568, tzinfo=datetime.timezone.utc), content='评审用例SAC-0001', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:13:58,881 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatStart sent by Unknown
2025-07-02 22:13:58,882 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='cd424942-e212-4731-87a9-99c4adbaab7d', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 13, 58, 878568, tzinfo=datetime.timezone.utc), content='评审用例SAC-0001', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:13:58,882 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='cd424942-e212-4731-87a9-99c4adbaab7d', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 13, 58, 878568, tzinfo=datetime.timezone.utc), content='评审用例SAC-0001', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:13:58,883 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:13:58,883 - autogen_core - INFO - Calling message handler for CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatStart published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:13:58,884 - autogen_core - INFO - Calling message handler for CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatStart published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:13:58,885 - autogen_core - INFO - Calling message handler for CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:13:59,246 - autogen_core - INFO - Resolving response with message type NoneType for recipient None from RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688: None
2025-07-02 22:14:00,170 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallRequestEvent(id='a8270df7-bfb5-450e-bd86-a2ba81f752a8', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=199, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 0, 170265, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], type='ToolCallRequestEvent')}
2025-07-02 22:14:00,171 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:00,174 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallExecutionEvent(id='e2fb2e0f-8fc3-46b6-816a-911102295766', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 0, 174253, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)], type='ToolCallExecutionEvent')}
2025-07-02 22:14:00,175 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallSummaryMessage(id='4d31db77-ac16-421e-8130-ee19324f2903', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 0, 175085, tzinfo=datetime.timezone.utc), content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)])}
2025-07-02 22:14:00,176 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=ToolCallSummaryMessage(id='4d31db77-ac16-421e-8130-ee19324f2903', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 0, 175085, tzinfo=datetime.timezone.utc), content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)]), inner_messages=[ToolCallRequestEvent(id='a8270df7-bfb5-450e-bd86-a2ba81f752a8', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=199, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 0, 170265, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(id='e2fb2e0f-8fc3-46b6-816a-911102295766', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 0, 174253, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)], type='ToolCallExecutionEvent')]), 'agent_name': 'CaseRetrieval'}
2025-07-02 22:14:00,177 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:00,177 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:00,178 - autogen_core - INFO - Calling message handler for CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:00,179 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:00,180 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:14:00,180 - autogen_core - INFO - Calling message handler for CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:21,540 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': TextMessage(id='22ebc535-5044-4be7-949c-7c7939c65508', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4041, completion_tokens=513), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 21, 539912, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证IPMI查看NVME硬盘满配环境列表信息功能”。\n\n2. 问题描述：描述中包含无效的图片链接，且未明确说明测试目的。\n   问题位置：描述。\n   改进建议：修改描述为“验证在满配NVME硬盘环境下，通过IPMI命令正确获取硬盘列表信息，包括硬盘总数、接口类型、槽位、序列号等”。\n\n3. 问题描述：操作2中的IPMI命令参数不完整，缺少必要的参数说明。\n   问题位置：测试步骤，操作2。\n   改进建议：操作2改为“通过IPMI命令查询NVME硬盘列表信息，命令参考：raw 0x2e 0x12 0x3e 0x0f 0 0 0x08（其中0x08表示请求8块硬盘信息）”。\n\n4. 问题描述：预期结果1未给出具体判断标准。\n   问题位置：测试步骤，预期结果1。\n   改进建议：预期结果1改为“所有8块NVME硬盘均正常工作，无告警或错误状态”。\n\n5. 问题描述：预期结果2未明确返回信息的具体格式或示例。\n   问题位置：测试步骤，预期结果2。\n   改进建议：预期结果2改为“返回码为0，响应数据包含硬盘总数为8，每块硬盘的接口类型为NVMe，槽位编号与实际物理位置一致，序列号非空”。\n\n6. 问题描述：预期结果3、4、5未明确“与实际一致”的具体验证方法。\n   问题位置：测试步骤，预期结果3、4、5。\n   改进建议：\n      预期结果3改为“整框掉电再上电后，通过IPMI命令查询到的NVME硬盘信息与掉电前记录的信息完全一致”。\n      预期结果4改为“host重启后，通过IPMI命令查询到的NVME硬盘信息与重启前记录的信息完全一致”。\n      预期结果5改为“BMC重启后，通过IPMI命令查询到的NVME硬盘信息与重启前记录的信息完全一致”。', type='TextMessage')}
2025-07-02 22:14:21,541 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=TextMessage(id='22ebc535-5044-4be7-949c-7c7939c65508', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4041, completion_tokens=513), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 21, 539912, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证IPMI查看NVME硬盘满配环境列表信息功能”。\n\n2. 问题描述：描述中包含无效的图片链接，且未明确说明测试目的。\n   问题位置：描述。\n   改进建议：修改描述为“验证在满配NVME硬盘环境下，通过IPMI命令正确获取硬盘列表信息，包括硬盘总数、接口类型、槽位、序列号等”。\n\n3. 问题描述：操作2中的IPMI命令参数不完整，缺少必要的参数说明。\n   问题位置：测试步骤，操作2。\n   改进建议：操作2改为“通过IPMI命令查询NVME硬盘列表信息，命令参考：raw 0x2e 0x12 0x3e 0x0f 0 0 0x08（其中0x08表示请求8块硬盘信息）”。\n\n4. 问题描述：预期结果1未给出具体判断标准。\n   问题位置：测试步骤，预期结果1。\n   改进建议：预期结果1改为“所有8块NVME硬盘均正常工作，无告警或错误状态”。\n\n5. 问题描述：预期结果2未明确返回信息的具体格式或示例。\n   问题位置：测试步骤，预期结果2。\n   改进建议：预期结果2改为“返回码为0，响应数据包含硬盘总数为8，每块硬盘的接口类型为NVMe，槽位编号与实际物理位置一致，序列号非空”。\n\n6. 问题描述：预期结果3、4、5未明确“与实际一致”的具体验证方法。\n   问题位置：测试步骤，预期结果3、4、5。\n   改进建议：\n      预期结果3改为“整框掉电再上电后，通过IPMI命令查询到的NVME硬盘信息与掉电前记录的信息完全一致”。\n      预期结果4改为“host重启后，通过IPMI命令查询到的NVME硬盘信息与重启前记录的信息完全一致”。\n      预期结果5改为“BMC重启后，通过IPMI命令查询到的NVME硬盘信息与重启前记录的信息完全一致”。', type='TextMessage'), inner_messages=[]), 'agent_name': 'CaseReviewer'}
2025-07-02 22:14:21,541 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:21,542 - autogen_core - INFO - Calling message handler for CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:21,542 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:21,543 - autogen_core - INFO - Publishing message of type GroupChatTermination to all subscribers: {'message': StopMessage(id='a214b339-76ca-4715-afe8-dcf70777569f', source='MaxMessageTermination', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 21, 543370, tzinfo=datetime.timezone.utc), content='Maximum number of messages 3 reached, current message count: 3', type='StopMessage'), 'error': None}
2025-07-02 22:14:31,611 - autogen_core - INFO - Sending message of type GroupChatStart to RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688: {'messages': [TextMessage(id='0bc5f676-f579-46d5-af05-28495a03c5ad', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 31, 610577, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:14:31,611 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatStart sent by Unknown
2025-07-02 22:14:31,612 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='0bc5f676-f579-46d5-af05-28495a03c5ad', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 31, 610577, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:14:31,612 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='0bc5f676-f579-46d5-af05-28495a03c5ad', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 31, 610577, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:14:31,613 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:14:31,614 - autogen_core - INFO - Calling message handler for CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatStart published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:31,614 - autogen_core - INFO - Calling message handler for CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatStart published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:31,615 - autogen_core - INFO - Calling message handler for CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:31,617 - autogen_core - INFO - Resolving response with message type NoneType for recipient None from RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688: None
2025-07-02 22:14:33,060 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallRequestEvent(id='66f5f08b-91b3-4cfd-ba5f-74b80314803a', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=1096, completion_tokens=28), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 33, 60565, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], type='ToolCallRequestEvent')}
2025-07-02 22:14:33,061 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:33,064 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallExecutionEvent(id='2eba78e2-ecbe-4a83-9be1-8bcfb6bb80ae', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 33, 64001, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)], type='ToolCallExecutionEvent')}
2025-07-02 22:14:33,065 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallSummaryMessage(id='7edc01b2-ca40-4cbc-ae60-2b598583e1e9', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 33, 64838, tzinfo=datetime.timezone.utc), content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)])}
2025-07-02 22:14:33,065 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=ToolCallSummaryMessage(id='7edc01b2-ca40-4cbc-ae60-2b598583e1e9', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 33, 64838, tzinfo=datetime.timezone.utc), content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)]), inner_messages=[ToolCallRequestEvent(id='66f5f08b-91b3-4cfd-ba5f-74b80314803a', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=1096, completion_tokens=28), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 33, 60565, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(id='2eba78e2-ecbe-4a83-9be1-8bcfb6bb80ae', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 33, 64001, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)], type='ToolCallExecutionEvent')]), 'agent_name': 'CaseRetrieval'}
2025-07-02 22:14:33,066 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:33,067 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:33,067 - autogen_core - INFO - Calling message handler for CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:33,068 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:33,069 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:14:33,069 - autogen_core - INFO - Calling message handler for CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:59,291 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': TextMessage(id='5e0b87a4-6c37-461a-9927-510ef6270c75', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4996, completion_tokens=732), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 59, 290842, tzinfo=datetime.timezone.utc), content="1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证SNMP配置SYSLOG服务器后其他接口发送SYSLOG功能”。\n\n2. 问题描述：描述为空，未明确说明测试目的和范围。\n   问题位置：描述。\n   改进建议：修改描述为“验证通过SNMP配置SYSLOG服务器后，使用REDFISH接口和WEB界面发送SYSLOG日志的功能是否正常，包括参数配置不全、未使能SYSLOG服务器等情况下的行为”。\n\n3. 问题描述：操作1中未明确说明如何设置syslogReceiverEnable、syslogReceiverAddress、syslogSendLogType参数。\n   问题位置：测试步骤，操作1。\n   改进建议：操作1改为“通过SNMP接口设置某一路配置为空的SYSLOG SERVER，设置syslogReceiverEnable为enable，syslogReceiverAddress和syslogSendLogType保留为空（或保留其中某一个为空），命令参考：snmpset -v2c -c private device_ip OID syslogReceiverEnable i 1, syslogReceiverAddress a '', syslogSendLogType i 0”。\n\n4. 问题描述：预期结果2未给出具体的失败判断标准。\n   问题位置：测试步骤，预期结果2。\n   改进建议：预期结果2改为“返回码为4xx或5xx，表示参数配置不全导致发送失败”。\n\n5. 问题描述：操作4中未明确说明如何设置syslogReceiverPort、syslogReceiverEnable参数。\n   问题位置：测试步骤，操作4。\n   改进建议：操作4改为“通过SNMP接口将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，设置syslogReceiverPort为实际端口（默认514），syslogReceiverEnable为disable，命令参考：snmpset -v2c -c private device_ip OID syslogReceiverAddress a 'server_address', syslogSendLogType i 1, syslogReceiverPort i 514, syslogReceiverEnable i 2”。\n\n6. 问题描述：预期结果5未明确说明SNMP发送测试失败的具体表现。\n   问题位置：测试步骤，预期结果5。\n   改进建议：预期结果5改为“SNMP发送测试失败，返回码为4xx或5xx；WEB界面测试按钮置灰，无法点击”。\n\n7. 问题描述：操作6中未明确说明如何设置syslogReceiverEnable参数。\n   问题位置：测试步骤，操作6。\n   改进建议：操作6改为“通过SNMP接口将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，设置syslogReceiverPort为实际端口（默认514），syslogReceiverEnable为enable，命令参考：snmpset -v2c -c private device_ip OID syslogReceiverAddress a 'server_address', syslogSendLogType i 1, syslogReceiverPort i 514, syslogReceiverEnable i 1”。\n\n8. 问题描述：预期结果7未明确说明SNMP发送测试成功的具体表现。\n   问题位置：测试步骤，预期结果7。\n   改进建议：预期结果7改为“SNMP发送测试成功，返回码为2xx；WEB界面测试按钮高亮，点击后显示发送成功”。", type='TextMessage')}
2025-07-02 22:14:59,291 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=TextMessage(id='5e0b87a4-6c37-461a-9927-510ef6270c75', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4996, completion_tokens=732), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 59, 290842, tzinfo=datetime.timezone.utc), content="1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证SNMP配置SYSLOG服务器后其他接口发送SYSLOG功能”。\n\n2. 问题描述：描述为空，未明确说明测试目的和范围。\n   问题位置：描述。\n   改进建议：修改描述为“验证通过SNMP配置SYSLOG服务器后，使用REDFISH接口和WEB界面发送SYSLOG日志的功能是否正常，包括参数配置不全、未使能SYSLOG服务器等情况下的行为”。\n\n3. 问题描述：操作1中未明确说明如何设置syslogReceiverEnable、syslogReceiverAddress、syslogSendLogType参数。\n   问题位置：测试步骤，操作1。\n   改进建议：操作1改为“通过SNMP接口设置某一路配置为空的SYSLOG SERVER，设置syslogReceiverEnable为enable，syslogReceiverAddress和syslogSendLogType保留为空（或保留其中某一个为空），命令参考：snmpset -v2c -c private device_ip OID syslogReceiverEnable i 1, syslogReceiverAddress a '', syslogSendLogType i 0”。\n\n4. 问题描述：预期结果2未给出具体的失败判断标准。\n   问题位置：测试步骤，预期结果2。\n   改进建议：预期结果2改为“返回码为4xx或5xx，表示参数配置不全导致发送失败”。\n\n5. 问题描述：操作4中未明确说明如何设置syslogReceiverPort、syslogReceiverEnable参数。\n   问题位置：测试步骤，操作4。\n   改进建议：操作4改为“通过SNMP接口将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，设置syslogReceiverPort为实际端口（默认514），syslogReceiverEnable为disable，命令参考：snmpset -v2c -c private device_ip OID syslogReceiverAddress a 'server_address', syslogSendLogType i 1, syslogReceiverPort i 514, syslogReceiverEnable i 2”。\n\n6. 问题描述：预期结果5未明确说明SNMP发送测试失败的具体表现。\n   问题位置：测试步骤，预期结果5。\n   改进建议：预期结果5改为“SNMP发送测试失败，返回码为4xx或5xx；WEB界面测试按钮置灰，无法点击”。\n\n7. 问题描述：操作6中未明确说明如何设置syslogReceiverEnable参数。\n   问题位置：测试步骤，操作6。\n   改进建议：操作6改为“通过SNMP接口将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，设置syslogReceiverPort为实际端口（默认514），syslogReceiverEnable为enable，命令参考：snmpset -v2c -c private device_ip OID syslogReceiverAddress a 'server_address', syslogSendLogType i 1, syslogReceiverPort i 514, syslogReceiverEnable i 1”。\n\n8. 问题描述：预期结果7未明确说明SNMP发送测试成功的具体表现。\n   问题位置：测试步骤，预期结果7。\n   改进建议：预期结果7改为“SNMP发送测试成功，返回码为2xx；WEB界面测试按钮高亮，点击后显示发送成功”。", type='TextMessage'), inner_messages=[]), 'agent_name': 'CaseReviewer'}
2025-07-02 22:14:59,292 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatMessage published by CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:59,293 - autogen_core - INFO - Calling message handler for CaseRetrieval_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:59,293 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_3dfabf5d-4f6f-4a50-8792-d93269cd7688 with message type GroupChatAgentResponse published by CaseReviewer_3dfabf5d-4f6f-4a50-8792-d93269cd7688/3dfabf5d-4f6f-4a50-8792-d93269cd7688
2025-07-02 22:14:59,294 - autogen_core - INFO - Publishing message of type GroupChatTermination to all subscribers: {'message': StopMessage(id='27915df9-a8c0-4fc9-875e-9b1c3bc2a5c1', source='MaxMessageTermination', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 14, 59, 294094, tzinfo=datetime.timezone.utc), content='Maximum number of messages 3 reached, current message count: 3', type='StopMessage'), 'error': None}
2025-07-02 22:17:20,482 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:17:20,482 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:17:21,520 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:17:21,520 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:17:21,520 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:17:21,520 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:18:26,469 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:18:26,469 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:18:27,638 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:18:27,638 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:18:27,638 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:18:27,638 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:19:41,825 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:19:41,825 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:20:45,272 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:20:45,272 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:20:45,273 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:20:45,273 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:20:48,476 - autogen_core - INFO - Sending message of type GroupChatStart to RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b: {'messages': [TextMessage(id='e5f0fd65-a58c-4be3-b8ba-ce11b3ab263f', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 48, 474265, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:20:48,477 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatStart sent by Unknown
2025-07-02 22:20:48,478 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='e5f0fd65-a58c-4be3-b8ba-ce11b3ab263f', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 48, 474265, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:20:48,478 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='e5f0fd65-a58c-4be3-b8ba-ce11b3ab263f', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 48, 474265, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:20:48,479 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:20:48,479 - autogen_core - INFO - Calling message handler for CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatStart published by RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:48,480 - autogen_core - INFO - Calling message handler for CaseReviewer_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatStart published by RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:48,481 - autogen_core - INFO - Calling message handler for CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:48,773 - autogen_core - INFO - Resolving response with message type NoneType for recipient None from RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b: None
2025-07-02 22:20:49,573 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallRequestEvent(id='824ddaba-e357-4839-8f4e-5f8bcb573563', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=199, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 49, 573415, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], type='ToolCallRequestEvent')}
2025-07-02 22:20:49,574 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatMessage published by CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:49,576 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallExecutionEvent(id='2146d5d1-571d-45be-997d-fa99650fbdee', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 49, 576627, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)], type='ToolCallExecutionEvent')}
2025-07-02 22:20:49,577 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallSummaryMessage(id='e17bb981-586a-44aa-a9a1-3c855e0e2325', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 49, 577281, tzinfo=datetime.timezone.utc), content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)])}
2025-07-02 22:20:49,578 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=ToolCallSummaryMessage(id='e17bb981-586a-44aa-a9a1-3c855e0e2325', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 49, 577281, tzinfo=datetime.timezone.utc), content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)]), inner_messages=[ToolCallRequestEvent(id='824ddaba-e357-4839-8f4e-5f8bcb573563', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=199, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 49, 573415, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(id='2146d5d1-571d-45be-997d-fa99650fbdee', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 20, 49, 576627, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)], type='ToolCallExecutionEvent')]), 'agent_name': 'CaseRetrieval'}
2025-07-02 22:20:49,578 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatMessage published by CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:49,579 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatMessage published by CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:49,579 - autogen_core - INFO - Calling message handler for CaseReviewer_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatAgentResponse published by CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:49,580 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatAgentResponse published by CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:20:49,581 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:20:49,581 - autogen_core - INFO - Calling message handler for CaseReviewer_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:21:14,323 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': TextMessage(id='32303e03-81a7-4a33-9b34-c1eaa3e02c0f', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4137, completion_tokens=663), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 21, 14, 322442, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证SNMP配置SYSLOG服务器后其他接口发送SYSLOG功能”。\n\n2. 问题描述：用例步骤操作1含多个操作，应当拆分为独立的操作及预期结果。\n   问题位置：测试步骤，操作1及预期结果1。\n   改进建议：将操作1拆分为两个独立的操作：\n      操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable\n      预期结果1：syslogReceiverEnable参数配置成功\n      操作2：保持syslogReceiverAddress、syslogSendLogType这两个参数为空（或保留其中某一个为空）\n      预期结果2：syslogReceiverAddress和syslogSendLogType参数保留为空\n\n3. 问题描述：测试步骤中，操作2需要执行接口调用，但并未给出接口的示例。\n   问题位置：测试步骤，操作2。\n   改进建议：在操作2中增加接口示例：\n      操作2：REDFISH发送测试日志，命令参考：\n      POST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\n      body：\n      {\n       "MemberId":member_id\n      }\n      member_id取值0~3\n\n4. 问题描述：测试步骤中，操作3需要执行web登录操作，但并未给出具体操作步骤。\n   问题位置：测试步骤，操作3。\n   改进建议：在操作3中增加具体操作步骤：\n      操作3：通过BMC web页面导航至Syslog服务器配置信息页面，查看配置信息\n\n5. 问题描述：测试步骤中，操作4和操作6需要执行接口调用或指令，但并未给出接口或指令的示例。\n   问题位置：测试步骤，操作4和操作6。\n   改进建议：在操作4和操作6中增加接口或指令示例：\n      操作4：通过SNMP接口设置syslogReceiverAddress、syslogSendLogType这两个参数，命令参考：snmpset -v2c -c public device_ip OID syslogReceiverAddress s "address" syslogSendLogType i 1 syslogReceiverPort i 514 syslogReceiverEnable i 2\n      预期结果4：syslogReceiverAddress、syslogSendLogType、syslogReceiverPort和syslogReceiverEnable参数配置成功\n      操作6：通过SNMP接口设置syslogReceiverAddress、syslogSendLogType这两个参数，命令参考：snmpset -v2c -c public device_ip OID syslogReceiverAddress s "address" syslogSendLogType i 1 syslogReceiverPort i 514 syslogReceiverEnable i 1\n      预期结果6：syslogReceiverAddress、syslogSendLogType、syslogReceiverPort和syslogReceiverEnable参数配置成功', type='TextMessage')}
2025-07-02 22:21:14,323 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=TextMessage(id='32303e03-81a7-4a33-9b34-c1eaa3e02c0f', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4137, completion_tokens=663), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 21, 14, 322442, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证SNMP配置SYSLOG服务器后其他接口发送SYSLOG功能”。\n\n2. 问题描述：用例步骤操作1含多个操作，应当拆分为独立的操作及预期结果。\n   问题位置：测试步骤，操作1及预期结果1。\n   改进建议：将操作1拆分为两个独立的操作：\n      操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable\n      预期结果1：syslogReceiverEnable参数配置成功\n      操作2：保持syslogReceiverAddress、syslogSendLogType这两个参数为空（或保留其中某一个为空）\n      预期结果2：syslogReceiverAddress和syslogSendLogType参数保留为空\n\n3. 问题描述：测试步骤中，操作2需要执行接口调用，但并未给出接口的示例。\n   问题位置：测试步骤，操作2。\n   改进建议：在操作2中增加接口示例：\n      操作2：REDFISH发送测试日志，命令参考：\n      POST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\n      body：\n      {\n       "MemberId":member_id\n      }\n      member_id取值0~3\n\n4. 问题描述：测试步骤中，操作3需要执行web登录操作，但并未给出具体操作步骤。\n   问题位置：测试步骤，操作3。\n   改进建议：在操作3中增加具体操作步骤：\n      操作3：通过BMC web页面导航至Syslog服务器配置信息页面，查看配置信息\n\n5. 问题描述：测试步骤中，操作4和操作6需要执行接口调用或指令，但并未给出接口或指令的示例。\n   问题位置：测试步骤，操作4和操作6。\n   改进建议：在操作4和操作6中增加接口或指令示例：\n      操作4：通过SNMP接口设置syslogReceiverAddress、syslogSendLogType这两个参数，命令参考：snmpset -v2c -c public device_ip OID syslogReceiverAddress s "address" syslogSendLogType i 1 syslogReceiverPort i 514 syslogReceiverEnable i 2\n      预期结果4：syslogReceiverAddress、syslogSendLogType、syslogReceiverPort和syslogReceiverEnable参数配置成功\n      操作6：通过SNMP接口设置syslogReceiverAddress、syslogSendLogType这两个参数，命令参考：snmpset -v2c -c public device_ip OID syslogReceiverAddress s "address" syslogSendLogType i 1 syslogReceiverPort i 514 syslogReceiverEnable i 1\n      预期结果6：syslogReceiverAddress、syslogSendLogType、syslogReceiverPort和syslogReceiverEnable参数配置成功', type='TextMessage'), inner_messages=[]), 'agent_name': 'CaseReviewer'}
2025-07-02 22:21:14,324 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatMessage published by CaseReviewer_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:21:14,324 - autogen_core - INFO - Calling message handler for CaseRetrieval_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatAgentResponse published by CaseReviewer_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:21:14,325 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_91b4532d-c2ee-4a21-b169-706a03f5a19b with message type GroupChatAgentResponse published by CaseReviewer_91b4532d-c2ee-4a21-b169-706a03f5a19b/91b4532d-c2ee-4a21-b169-706a03f5a19b
2025-07-02 22:21:14,325 - autogen_core - INFO - Publishing message of type GroupChatTermination to all subscribers: {'message': StopMessage(id='51f70509-4670-4d08-bc81-2aab35a0e6fe', source='MaxMessageTermination', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 21, 14, 325739, tzinfo=datetime.timezone.utc), content='Maximum number of messages 3 reached, current message count: 3', type='StopMessage'), 'error': None}
2025-07-02 22:21:44,534 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:21:44,534 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:36:25,455 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:36:25,456 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:36:25,456 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:36:25,456 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:36:35,407 - autogen_core - INFO - Sending message of type GroupChatStart to RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265: {'messages': [TextMessage(id='f8228972-4e13-4492-a747-773501c95e9d', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 35, 405538, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:36:35,408 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatStart sent by Unknown
2025-07-02 22:36:35,409 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='f8228972-4e13-4492-a747-773501c95e9d', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 35, 405538, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:36:35,410 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='f8228972-4e13-4492-a747-773501c95e9d', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 35, 405538, tzinfo=datetime.timezone.utc), content='评审用例SAC-0002', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:36:35,411 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:36:35,411 - autogen_core - INFO - Calling message handler for CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatStart published by RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:35,412 - autogen_core - INFO - Calling message handler for CaseReviewer_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatStart published by RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:35,413 - autogen_core - INFO - Calling message handler for CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:35,668 - autogen_core - INFO - Resolving response with message type NoneType for recipient None from RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265: None
2025-07-02 22:36:36,505 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallRequestEvent(id='d794734d-1bfd-4d98-9e33-08cbd1904dcd', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=199, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 36, 505314, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], type='ToolCallRequestEvent')}
2025-07-02 22:36:36,506 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatMessage published by CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:36,508 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallExecutionEvent(id='3e076f83-c3ba-4223-8553-74426e7f63e0', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 36, 508082, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)], type='ToolCallExecutionEvent')}
2025-07-02 22:36:36,509 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallSummaryMessage(id='a42e663e-a43b-413a-928b-9d5657d8ee4d', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 36, 508697, tzinfo=datetime.timezone.utc), content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)])}
2025-07-02 22:36:36,509 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=ToolCallSummaryMessage(id='a42e663e-a43b-413a-928b-9d5657d8ee4d', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 36, 508697, tzinfo=datetime.timezone.utc), content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)]), inner_messages=[ToolCallRequestEvent(id='d794734d-1bfd-4d98-9e33-08cbd1904dcd', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=199, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 36, 505314, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', arguments='{"case_id": "SAC-0002"}', name='case_search_tool')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(id='3e076f83-c3ba-4223-8553-74426e7f63e0', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 36, 36, 508082, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试\n        【描述】：\n        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器\n        【测试步骤】：\n            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）\n            预期结果1：配置完成\n            操作2：REDFISH发送测试日志\nPOST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent\nbody：\n{\n "MemberId":member_id\n}\nmember_id取值0~3\n    预期结果2：参数配置不全时发送失败\n    操作3：登陆web查看Syslog服务器配置信息\n    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰\n    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable\n    预期结果4：配置完成\n    操作5：重复步骤2~3\n    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰\n    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable\n    预期结果6：配置完成\n    操作7：重复步骤2~3\n    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功\n        ', name='case_search_tool', call_id='call_02eee08d-6e95-5c5c-bcf8-17da5b4b3e2f', is_error=False)], type='ToolCallExecutionEvent')]), 'agent_name': 'CaseRetrieval'}
2025-07-02 22:36:36,510 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatMessage published by CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:36,510 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatMessage published by CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:36,511 - autogen_core - INFO - Calling message handler for CaseReviewer_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatAgentResponse published by CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:36,511 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatAgentResponse published by CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:36:36,512 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:36:36,512 - autogen_core - INFO - Calling message handler for CaseReviewer_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:37:05,072 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': TextMessage(id='04d57305-f33c-439c-8732-04cdded2c1dd', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4137, completion_tokens=880), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 37, 5, 71614, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证SNMP配置SYSLOG服务器后其他接口发送SYSLOG功能”。\n\n2. 问题描述：用例步骤操作1含多个操作，应当拆分为独立的操作及预期结果。\n   问题位置：测试步骤，操作1及预期结果1。\n   改进建议：将操作1拆分为两个独立的操作：\n      操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable\n      预期结果1：syslogReceiverEnable参数配置成功\n      操作2：保留syslogReceiverAddress、syslogSendLogType这两个参数为空（或保留其中某一个为空）\n      预期结果2：syslogReceiverAddress和syslogSendLogType参数保留为空\n\n3. 问题描述：测试步骤中，操作2需要执行接口调用，但并未给出接口的示例。\n   问题位置：测试步骤，操作2。\n   改进建议：在操作2中明确接口调用的示例，如：\n      操作2：通过REDFISH接口POST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent发送测试日志，请求消息体：\n      {\n       "MemberId":member_id\n      }\n      member_id取值0~3\n\n4. 问题描述：测试步骤中，操作3需要执行web登录操作，但并未给出具体操作步骤。\n   问题位置：测试步骤，操作3。\n   改进建议：在操作3中明确web登录操作的具体步骤，如：\n      操作3：使用管理员账号登录BMC web页面，导航至Syslog服务器配置信息页面\n\n5. 问题描述：测试步骤中，操作4和操作6包含多个参数配置，应当拆分为独立的操作及预期结果。\n   问题位置：测试步骤，操作4及预期结果4；操作6及预期结果6。\n   改进建议：将操作4拆分为三个独立的操作：\n      操作4：将第一步中的syslogReceiverAddress配置完整\n      预期结果4：syslogReceiverAddress参数配置成功\n      操作5：将第一步中的syslogSendLogType配置完整\n      预期结果5：syslogSendLogType参数配置成功\n      操作6：将syslogReceiverPort改为和实际一致（默认514）\n      预期结果6：syslogReceiverPort参数配置成功\n      操作7：将syslogReceiverEnable改为disable\n      预期结果7：syslogReceiverEnable参数配置成功\n      同样地，将操作6拆分为三个独立的操作。\n\n6. 问题描述：测试步骤中，操作7需要重复步骤2~3，但并未给出具体操作步骤。\n   问题位置：测试步骤，操作7。\n   改进建议：在操作7中明确重复步骤2~3的具体操作步骤，如：\n      操作7：重复操作2（通过REDFISH接口发送测试日志）和操作3（登录web查看Syslog服务器配置信息）\n\n7. 问题描述：测试步骤中，预期结果2、预期结果5、预期结果7描述模糊，没有给出明确的失败条件。\n   问题位置：测试步骤，预期结果2、预期结果5、预期结果7。\n   改进建议：明确预期结果中的失败条件，如：\n      预期结果2：当syslogReceiverAddress或syslogSendLogType为空时，发送失败，返回码为4xx\n      预期结果5：当syslogReceiverEnable为disable时，SNMP发送测试失败，返回码为4xx，web测试按钮置灰\n      预期结果7：当syslogReceiverEnable为enable且参数配置完整时，SNMP发送测试成功，返回码为2xx，web测试按钮高亮，点击测试发送成功', type='TextMessage')}
2025-07-02 22:37:05,073 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=TextMessage(id='04d57305-f33c-439c-8732-04cdded2c1dd', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4137, completion_tokens=880), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 37, 5, 71614, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证SNMP配置SYSLOG服务器后其他接口发送SYSLOG功能”。\n\n2. 问题描述：用例步骤操作1含多个操作，应当拆分为独立的操作及预期结果。\n   问题位置：测试步骤，操作1及预期结果1。\n   改进建议：将操作1拆分为两个独立的操作：\n      操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable\n      预期结果1：syslogReceiverEnable参数配置成功\n      操作2：保留syslogReceiverAddress、syslogSendLogType这两个参数为空（或保留其中某一个为空）\n      预期结果2：syslogReceiverAddress和syslogSendLogType参数保留为空\n\n3. 问题描述：测试步骤中，操作2需要执行接口调用，但并未给出接口的示例。\n   问题位置：测试步骤，操作2。\n   改进建议：在操作2中明确接口调用的示例，如：\n      操作2：通过REDFISH接口POST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent发送测试日志，请求消息体：\n      {\n       "MemberId":member_id\n      }\n      member_id取值0~3\n\n4. 问题描述：测试步骤中，操作3需要执行web登录操作，但并未给出具体操作步骤。\n   问题位置：测试步骤，操作3。\n   改进建议：在操作3中明确web登录操作的具体步骤，如：\n      操作3：使用管理员账号登录BMC web页面，导航至Syslog服务器配置信息页面\n\n5. 问题描述：测试步骤中，操作4和操作6包含多个参数配置，应当拆分为独立的操作及预期结果。\n   问题位置：测试步骤，操作4及预期结果4；操作6及预期结果6。\n   改进建议：将操作4拆分为三个独立的操作：\n      操作4：将第一步中的syslogReceiverAddress配置完整\n      预期结果4：syslogReceiverAddress参数配置成功\n      操作5：将第一步中的syslogSendLogType配置完整\n      预期结果5：syslogSendLogType参数配置成功\n      操作6：将syslogReceiverPort改为和实际一致（默认514）\n      预期结果6：syslogReceiverPort参数配置成功\n      操作7：将syslogReceiverEnable改为disable\n      预期结果7：syslogReceiverEnable参数配置成功\n      同样地，将操作6拆分为三个独立的操作。\n\n6. 问题描述：测试步骤中，操作7需要重复步骤2~3，但并未给出具体操作步骤。\n   问题位置：测试步骤，操作7。\n   改进建议：在操作7中明确重复步骤2~3的具体操作步骤，如：\n      操作7：重复操作2（通过REDFISH接口发送测试日志）和操作3（登录web查看Syslog服务器配置信息）\n\n7. 问题描述：测试步骤中，预期结果2、预期结果5、预期结果7描述模糊，没有给出明确的失败条件。\n   问题位置：测试步骤，预期结果2、预期结果5、预期结果7。\n   改进建议：明确预期结果中的失败条件，如：\n      预期结果2：当syslogReceiverAddress或syslogSendLogType为空时，发送失败，返回码为4xx\n      预期结果5：当syslogReceiverEnable为disable时，SNMP发送测试失败，返回码为4xx，web测试按钮置灰\n      预期结果7：当syslogReceiverEnable为enable且参数配置完整时，SNMP发送测试成功，返回码为2xx，web测试按钮高亮，点击测试发送成功', type='TextMessage'), inner_messages=[]), 'agent_name': 'CaseReviewer'}
2025-07-02 22:37:05,073 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatMessage published by CaseReviewer_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:37:05,074 - autogen_core - INFO - Calling message handler for CaseRetrieval_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatAgentResponse published by CaseReviewer_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:37:05,074 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_69219e50-9a4c-415c-9ced-61600cbff265 with message type GroupChatAgentResponse published by CaseReviewer_69219e50-9a4c-415c-9ced-61600cbff265/69219e50-9a4c-415c-9ced-61600cbff265
2025-07-02 22:37:05,075 - autogen_core - INFO - Publishing message of type GroupChatTermination to all subscribers: {'message': StopMessage(id='027a24d4-4524-4ff0-8029-1ffd98110287', source='MaxMessageTermination', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 37, 5, 75626, tzinfo=datetime.timezone.utc), content='Maximum number of messages 3 reached, current message count: 3', type='StopMessage'), 'error': None}
2025-07-02 22:37:09,185 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:37:09,185 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:40:48,752 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:40:48,752 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:40:48,752 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:40:48,752 - autogen_framework.main - INFO - Application startup complete
2025-07-02 22:45:03,764 - autogen_core - INFO - Sending message of type GroupChatStart to RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2: {'messages': [TextMessage(id='409916ea-bf7b-42c5-8fb8-e81be3b4ad79', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 3, 762438, tzinfo=datetime.timezone.utc), content='评审下用例SAC-0001', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:45:03,765 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatStart sent by Unknown
2025-07-02 22:45:03,767 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='409916ea-bf7b-42c5-8fb8-e81be3b4ad79', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 3, 762438, tzinfo=datetime.timezone.utc), content='评审下用例SAC-0001', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:45:03,767 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='409916ea-bf7b-42c5-8fb8-e81be3b4ad79', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 3, 762438, tzinfo=datetime.timezone.utc), content='评审下用例SAC-0001', type='TextMessage')], 'output_task_messages': True}
2025-07-02 22:45:03,768 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:45:03,768 - autogen_core - INFO - Calling message handler for CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatStart published by RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:03,769 - autogen_core - INFO - Calling message handler for CaseReviewer_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatStart published by RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:03,770 - autogen_core - INFO - Calling message handler for CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:04,117 - autogen_core - INFO - Resolving response with message type NoneType for recipient None from RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2: None
2025-07-02 22:45:04,958 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallRequestEvent(id='828efe3b-3c6b-4f95-80b5-6d78fd1ff4bf', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=200, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 4, 958500, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], type='ToolCallRequestEvent')}
2025-07-02 22:45:04,959 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatMessage published by CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:04,963 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallExecutionEvent(id='2c7e0e92-7a3e-461f-8f0a-465b61442c57', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 4, 962813, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)], type='ToolCallExecutionEvent')}
2025-07-02 22:45:04,963 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': ToolCallSummaryMessage(id='bbc4dfb8-9867-4991-8fc0-e27b05c182e8', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 4, 963457, tzinfo=datetime.timezone.utc), content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)])}
2025-07-02 22:45:04,964 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=ToolCallSummaryMessage(id='bbc4dfb8-9867-4991-8fc0-e27b05c182e8', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 4, 963457, tzinfo=datetime.timezone.utc), content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', type='ToolCallSummaryMessage', tool_calls=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], results=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)]), inner_messages=[ToolCallRequestEvent(id='828efe3b-3c6b-4f95-80b5-6d78fd1ff4bf', source='CaseRetrieval', models_usage=RequestUsage(prompt_tokens=200, completion_tokens=29), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 4, 958500, tzinfo=datetime.timezone.utc), content=[FunctionCall(id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', arguments='{"case_id": "SAC-0001"}', name='case_search_tool')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(id='2c7e0e92-7a3e-461f-8f0a-465b61442c57', source='CaseRetrieval', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 4, 962813, tzinfo=datetime.timezone.utc), content=[FunctionExecutionResult(content='\n        【标题】：IPMI查看NVME硬盘满配环境 列表信息\n        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>\n        【预置条件】：BMC上电，host上电\n        【测试步骤】：\n            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘\n            预期结果1：硬盘工作正常\n            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0\n            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n            操作3：整框掉电再上电，查看NVME信息\n            预期结果3：返回NVME信息与实际一致\n            操作4：host重启\n            预期结果4：返回NVME信息与实际一致\n            操作5：BMC重启\n            预期结果5：返回NVME信息与实际一致\n        ', name='case_search_tool', call_id='call_e7d91e13-4324-583f-8919-7de72f0c0bd8', is_error=False)], type='ToolCallExecutionEvent')]), 'agent_name': 'CaseRetrieval'}
2025-07-02 22:45:04,966 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatMessage published by CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:04,966 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatMessage published by CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:04,967 - autogen_core - INFO - Calling message handler for CaseReviewer_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatAgentResponse published by CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:04,967 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatAgentResponse published by CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:04,968 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 22:45:04,969 - autogen_core - INFO - Calling message handler for CaseReviewer_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:15,822 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': TextMessage(id='d9180e5f-ce9e-4545-935f-d61143b3659d', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4042, completion_tokens=244), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 15, 821724, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证IPMI查看NVME硬盘满配环境列表信息功能”。\n\n2. 问题描述：用例步骤操作2中通过接口、命令执行某个操作时未提供具体指令和参数。\n   问题位置：测试步骤，操作2。\n   改进建议：测试步骤改为如下：\n     操作2：通过ipmi命令查询NVME硬盘列表信息，命令参考：raw 0x2e 0x12 0x3e 0x0f 0 0\n     预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n\n3. 问题描述：用例步骤操作1预期结果1描述模糊，没有给出明确的判断标准。\n   问题位置：测试步骤，预期结果1。\n   改进建议：预期结果1改为“硬盘工作正常，无告警信息，且可以通过相关接口或命令检测到硬盘状态为正常”。', type='TextMessage')}
2025-07-02 22:45:15,823 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=TextMessage(id='d9180e5f-ce9e-4545-935f-d61143b3659d', source='CaseReviewer', models_usage=RequestUsage(prompt_tokens=4042, completion_tokens=244), metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 15, 821724, tzinfo=datetime.timezone.utc), content='1. 问题描述：标题未体现测试目的，不符合动词+宾语结构。\n   问题位置：标题。\n   改进建议：标题改为“验证IPMI查看NVME硬盘满配环境列表信息功能”。\n\n2. 问题描述：用例步骤操作2中通过接口、命令执行某个操作时未提供具体指令和参数。\n   问题位置：测试步骤，操作2。\n   改进建议：测试步骤改为如下：\n     操作2：通过ipmi命令查询NVME硬盘列表信息，命令参考：raw 0x2e 0x12 0x3e 0x0f 0 0\n     预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号\n\n3. 问题描述：用例步骤操作1预期结果1描述模糊，没有给出明确的判断标准。\n   问题位置：测试步骤，预期结果1。\n   改进建议：预期结果1改为“硬盘工作正常，无告警信息，且可以通过相关接口或命令检测到硬盘状态为正常”。', type='TextMessage'), inner_messages=[]), 'agent_name': 'CaseReviewer'}
2025-07-02 22:45:15,823 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatMessage published by CaseReviewer_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:15,824 - autogen_core - INFO - Calling message handler for CaseRetrieval_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatAgentResponse published by CaseReviewer_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:15,824 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_7b1831b7-2605-404c-9da5-15a7806bc3e2 with message type GroupChatAgentResponse published by CaseReviewer_7b1831b7-2605-404c-9da5-15a7806bc3e2/7b1831b7-2605-404c-9da5-15a7806bc3e2
2025-07-02 22:45:15,825 - autogen_core - INFO - Publishing message of type GroupChatTermination to all subscribers: {'message': StopMessage(id='33ccc005-bad7-4c1a-9c58-83b0d0c55fd0', source='MaxMessageTermination', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 2, 14, 45, 15, 825069, tzinfo=datetime.timezone.utc), content='Maximum number of messages 3 reached, current message count: 3', type='StopMessage'), 'error': None}
2025-07-02 22:56:47,805 - autogen_framework.main - INFO - Shutting down application
2025-07-02 22:56:47,806 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 22:56:49,527 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 22:56:49,528 - autogen_framework.main - INFO - Environment: development
2025-07-02 22:56:49,528 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 22:56:49,528 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:26:14,922 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:26:14,924 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:26:18,716 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:26:18,717 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:26:18,717 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:26:18,717 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:26:22,745 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:26:22,745 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:26:24,175 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:26:24,176 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:26:24,176 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:26:24,176 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:26:52,539 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:26:52,540 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:26:54,013 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:26:54,013 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:26:54,013 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:26:54,013 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:27:27,662 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:27:27,662 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:27:29,489 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:27:29,489 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:27:29,489 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:27:29,489 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:29:33,618 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:29:33,618 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:29:36,112 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:29:36,112 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:29:36,112 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:29:36,112 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:29:37,944 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:29:37,945 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:29:39,479 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:29:39,479 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:29:39,479 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:29:39,479 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:29:52,171 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:29:52,171 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:29:53,393 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:29:53,393 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:29:53,393 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:29:53,393 - autogen_framework.main - INFO - Application startup complete
2025-07-03 09:30:04,167 - autogen_framework.main - INFO - Shutting down application
2025-07-03 09:30:04,168 - autogen_framework.main - INFO - Application shutdown complete
2025-07-03 09:30:05,421 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-03 09:30:05,421 - autogen_framework.main - INFO - Environment: development
2025-07-03 09:30:05,421 - autogen_framework.main - INFO - Debug mode: True
2025-07-03 09:30:05,421 - autogen_framework.main - INFO - Application startup complete
