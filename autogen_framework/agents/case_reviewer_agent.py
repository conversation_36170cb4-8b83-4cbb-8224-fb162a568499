from typing import Any, Optional
from managers.agent_manager import get_agent_manager


async def case_search_tool(case_id: str) -> str:
    if case_id == "SAC-0001":
        return """
        【标题】：IPMI查看NVME硬盘满配环境 列表信息
        【描述】：<p><img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/ceccc1b1-9650-48fb-a92d-42bb9fd8812f/00067549" title="image.png" alt="image.png" /></p>
        【预置条件】：BMC上电，host上电
        【测试步骤】：
            操作1：环境中插入SBF24MA或者BP5505EM24LA 硬盘背板，满配8块NVME硬盘
            预期结果1：硬盘工作正常
            操作2：查询NVME硬盘列表信息，0x2e 0x12 0x3e 0x0f 0 0
            预期结果2：返回所有硬盘信息，内容包括硬盘总数、接口类型、槽位、序列号
            操作3：整框掉电再上电，查看NVME信息
            预期结果3：返回NVME信息与实际一致
            操作4：host重启
            预期结果4：返回NVME信息与实际一致
            操作5：BMC重启
            预期结果5：返回NVME信息与实际一致
        """ 
    if case_id == "SAC-0002":
        return """
        【标题】：SNMP配置SYSLOG服务器后其他接口发送SYSLOG测试
        【描述】：
        【预置条件】：BMC正常，某一路SYSLOG服务器配置为空；已搭建syslog服务器
        【测试步骤】：
            操作1：SNMP设置某一路配置为空的SYSLOG SERVER的syslogReceiverEnable为enable， syslogReceiverAddress、syslogSendLogType这两个参数保留为空（或保留其中某一个为空）
            预期结果1：配置完成
            操作2：REDFISH发送测试日志
POST https://device_ip/redfish/v1/Managers/1/SyslogService/Actions/SyslogService.SubmitTestEvent
body：
{
 "MemberId":member_id
}
member_id取值0~3
    预期结果2：参数配置不全时发送失败
    操作3：登陆web查看Syslog服务器配置信息
    预期结果3：web上显示的Syslog服务器信息和第一步设置一致，参数配置不全时测试按钮置灰
    操作4：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为disable
    预期结果4：配置完成
    操作5：重复步骤2~3
    预期结果5：未使能syslog server时SNMP发送测试失败，web测试按钮置灰
    操作6：将第一步中的syslogReceiverAddress、syslogSendLogType这两个参数配置完整，syslogReceiverPort改为和实际一致（默认514），syslogReceiverEnable改为enable
    预期结果6：配置完成
    操作7：重复步骤2~3
    预期结果7：SNMP发送测试成功；web测试按钮高亮，点击测试发送成功
        """ 

def case_retrieval_agent() -> Any:
    agent_manager = get_agent_manager()
    agent = agent_manager.create_autogen_agent(
        agent_class='AssistantAgent',
        model_alias='nebulacoder-v6.0',
        name='CaseRetrieval',
        tools=[case_search_tool],
        description='使用工具获取指定用例信息，用例id形如SAC-0001，以SAC-开头，后面是数字。'
    )
    return agent

def case_reviewer_agent(prompt_manager: Optional[Any] = None) -> Any:
    """
    用例评审Agent

    Returns:
        AssistantAgent实例
    """
    agent_manager = get_agent_manager()

    system_prompt = prompt_manager.get_prompt("agents/case_reviewer")
    agent = agent_manager.create_autogen_agent(
        agent_class='AssistantAgent',
        model_alias='nebulacoder-v6.0',
        name='CaseReviewer',
        description='服务器领域测试用例评审专家Agent，能够分析测试用例问题并提供改进建议。',
        system_message=system_prompt
    )
    return agent