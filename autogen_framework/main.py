"""
AutoGen Multi-Agent Framework - FastAPI Application Entry Point

This is the main entry point for the AutoGen Multi-Agent Framework.
It sets up the FastAPI application with all necessary routers and middleware.

Uses Microsoft's official autogen-agentchat package for multi-agent functionality.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
from datetime import datetime

# Import routers
from routers.agent_router import router as agent_router
from routers.team_router import router as team_router

# Import infrastructure
from infrastructure.logging_config import setup_logging, get_framework_logger
from infrastructure.config_manager import get_config
from infrastructure.exceptions import AutoGenFrameworkException, create_error_response

# Application metadata
APP_NAME = "AutoGen Multi-Agent Framework"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Enterprise-grade multi-agent system based on AutoGen and FastAPI"


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    print(f"🚀 Starting {APP_NAME} v{APP_VERSION}")

    try:
        # Initialize configuration
        config = get_config()
        print(f"📋 Configuration loaded: {config.app.environment} environment")

        # Initialize AutoGen logging
        logging_config = {
            "level": config.logging.level,
            "format": config.logging.format,
            "file_logging": {
                "enabled": True,
                "filename": config.logging.file or "autogen_framework.log"
            }
        }
        setup_logging(logging_config)

        # Get framework logger
        logger = get_framework_logger("main")
        logger.info(f"Starting {APP_NAME} v{APP_VERSION}")
        logger.info(f"Environment: {config.app.environment}")
        logger.info(f"Debug mode: {config.app.debug}")

        # Initialize managers (will be implemented in later phases)
        # await initialize_managers()

        logger.info("Application startup complete")
        print("✅ Application startup complete")

    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        raise

    yield

    # Shutdown
    try:
        logger = get_framework_logger("main")
        logger.info("Shutting down application")
        print("🛑 Shutting down application")

        # Cleanup resources if needed

        logger.info("Application shutdown complete")
        print("✅ Application shutdown complete")

    except Exception as e:
        print(f"❌ Error during shutdown: {e}")


# Create FastAPI application
def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    # 获取配置（如果配置未加载，将使用默认值）
    try:
        config = get_config()
        api_config = config.api
    except:
        # 使用默认配置
        from infrastructure.config_manager import APIConfig
        api_config = APIConfig()

    # 创建应用
    app = FastAPI(
        title=APP_NAME,
        version=APP_VERSION,
        description=APP_DESCRIPTION,
        lifespan=lifespan,
        docs_url=api_config.docs_url,
        redoc_url=api_config.redoc_url,
        openapi_url=api_config.openapi_url
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=api_config.cors_origins,
        allow_credentials=api_config.cors_credentials,
        allow_methods=api_config.cors_methods,
        allow_headers=api_config.cors_headers,
    )

    return app

# Create application instance
app = create_app()


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "app_name": APP_NAME,
        "version": APP_VERSION,
        "description": APP_DESCRIPTION,
        "message": "AutoGen Multi-Agent Framework is running"
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled exceptions."""
    logger = get_framework_logger("exception_handler")

    # Handle framework exceptions
    if isinstance(exc, AutoGenFrameworkException):
        logger.warning(f"Framework exception: {exc}")
        error_response = create_error_response(exc)
        error_response["timestamp"] = datetime.now().isoformat()

        return JSONResponse(
            status_code=400,
            content=error_response
        )

    # Handle unexpected exceptions
    logger.error(f"Unhandled exception: {exc}", exc_info=True)

    # Create generic error response
    framework_exc = AutoGenFrameworkException(
        message="An unexpected error occurred",
        details={"original_error": str(exc), "error_type": type(exc).__name__},
        cause=exc
    )

    error_response = create_error_response(framework_exc)
    error_response["timestamp"] = datetime.now().isoformat()

    return JSONResponse(
        status_code=500,
        content=error_response
    )


# Include routers
app.include_router(agent_router, tags=["agents"])
app.include_router(team_router, tags=["teams"])


if __name__ == "__main__":
    import os
    os.environ['https_proxy'] = ''
    os.environ['http_proxy'] = ''
    os.environ['all_proxy'] = ''
    os.environ['ftp_proxy'] = ''
    os.environ['no_proxy'] = ''
    # Get configuration for server settings
    try:
        config = get_config()
        host = config.app.host
        port = config.app.port
        debug = config.app.debug
        log_level = config.logging.level.lower()
    except:
        # Fallback to defaults
        host = "0.0.0.0"
        port = 8000
        debug = True
        log_level = "info"

    # Run the application
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level=log_level
    )
