"""
Team服务层

提供Team相关的业务逻辑服务，封装Team管理器的调用。
支持Team的创建、执行和管理功能。
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

try:
    from autogen_core import TRACE_LOGGER_NAME
    AUTOGEN_AVAILABLE = True
except ImportError:
    TRACE_LOGGER_NAME = "autogen_core.trace"
    AUTOGEN_AVAILABLE = False

from managers.team_manager import TeamManager
from managers.agent_manager import AgentManager
from infrastructure.exceptions import ServiceError, TeamError

# 使用AutoGen的trace logger
logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.team_service")


class TeamService:
    """Team服务类"""
    
    def __init__(self, team_manager: Optional[TeamManager] = None, agent_manager: Optional[AgentManager] = None):
        """
        初始化Team服务
        
        Args:
            team_manager: Team管理器实例，如果为None则创建新实例
            agent_manager: Agent管理器实例，如果为None则创建新实例
        """
        self.team_manager = team_manager or TeamManager()
        self.agent_manager = agent_manager or AgentManager()
        logger.info("TeamService initialized")
    
    async def run(self, name: str, message: str, **kwargs) -> Dict[str, Any]:
        """
        执行Team任务

        Args:
            name: Team名称/类型
            message: 用户消息
            **kwargs: 其他参数

        Returns:
            执行结果字典
        """
        start_time = datetime.now()

        try:
            logger.info(f"Starting team run: {name}")

            # 验证参数
            self._validate_run_params(name, message)
            
            # 创建Team实例
            team = self._get_team_instance(name, **kwargs)
            
            # 执行Team任务
            result = await self._execute_team(team, message, **kwargs)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Team run completed: {name}, execution_time: {execution_time:.2f}s")
            
            return {
                "status": "success",
                "result": result,
                "metadata": {
                    "team_name": name,
                    "message_length": len(message),
                    "execution_time": execution_time,
                    "timestamp": start_time.isoformat()
                }
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Team run failed: {name}, error: {str(e)}, execution_time: {execution_time:.2f}s")
            raise ServiceError(f"Team execution failed: {str(e)}")
    
    async def list_teams(self) -> Dict[str, Any]:
        """
        列出所有可用的Team
        
        Returns:
            包含Team列表和详细信息的字典
        """
        try:
            logger.info("Listing available teams")
            
            available_teams = self.team_manager.list_available_teams()
            
            # 获取每个Team的详细信息
            teams_info = []
            for team_name in available_teams:
                try:
                    info = self.team_manager.get_team_info(team_name)
                    teams_info.append({
                        "name": team_name,
                        "factory_function": info.get("factory_function", "unknown"),
                        "module": info.get("module", "unknown"),
                        "signature": info.get("signature", "unknown")
                    })
                except Exception as e:
                    logger.warning(f"Failed to get info for team {team_name}: {e}")
                    teams_info.append({
                        "name": team_name,
                        "factory_function": "unknown",
                        "module": "unknown",
                        "signature": "unknown",
                        "error": str(e)
                    })
            
            logger.info(f"Listed {len(available_teams)} teams")
            
            return {
                "status": "success",
                "teams": teams_info,
                "count": len(available_teams),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to list teams: {str(e)}")
            raise ServiceError(f"Failed to list teams: {str(e)}")
    
    async def get_team_info(self, name: str) -> Dict[str, Any]:
        """
        获取指定Team的详细信息
        
        Args:
            name: Team名称
        
        Returns:
            Team详细信息字典
        """
        try:
            logger.info(f"Getting team info: {name}")
            
            # 验证Team是否存在
            available_teams = self.team_manager.list_available_teams()
            if name not in available_teams:
                raise TeamError(f"Team '{name}' not found. Available teams: {available_teams}")
            
            # 获取Team信息
            info = self.team_manager.get_team_info(name)
            
            logger.info(f"Retrieved team info: {name}")
            
            return {
                "status": "success",
                "team_info": info,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get team info for {name}: {str(e)}")
            raise ServiceError(f"Failed to get team info: {str(e)}")
    
    def _validate_run_params(self, name: str, message: str) -> None:
        """
        验证运行参数

        Args:
            name: Team名称
            message: 用户消息
            model: 模型别名（可选）

        Raises:
            ServiceError: 参数验证失败
        """
        if not name or not isinstance(name, str):
            raise ServiceError("Team name must be a non-empty string")

        if not message or not isinstance(message, str):
            raise ServiceError("Message must be a non-empty string")
        
        # 验证Team是否存在
        available_teams = self.team_manager.list_available_teams()
        if name not in available_teams:
            raise ServiceError(f"Team '{name}' not found. Available teams: {available_teams}")
    
    def _get_team_instance(self, name: str, **kwargs):
        """
        获取Team实例

        Args:
            name: Team名称
            model: 模型别名（可选）
            **kwargs: 其他参数

        Returns:
            Team实例
        """
        try:
            # 从kwargs中提取Team特定的参数
            team_kwargs = kwargs.copy()
            
            # 移除服务层特定的参数
            service_params = ['timeout', 'max_retries', 'stream']
            for param in service_params:
                team_kwargs.pop(param, None)
            
            # 创建Team实例
            team = self.team_manager.create_team(name, **team_kwargs)
            
            logger.debug(f"Created team instance: {name}, type: {type(team)}")
            return team
            
        except Exception as e:
            logger.error(f"Failed to create team instance {name}: {str(e)}")
            raise TeamError(f"Failed to create team instance: {str(e)}")
    
    async def _execute_team(self, team, message: str, **kwargs) -> Any:
        """
        执行Team任务

        Args:
            team: Team实例
            message: 用户消息
            **kwargs: 其他参数

        Returns:
            Team执行结果
        """
        try:
            # 获取执行参数
            timeout = kwargs.get('timeout', 60)  # Team默认60秒超时
            stream = kwargs.get('stream', False)  # 是否使用流式输出

            # 如果AutoGen不可用，返回模拟结果
            if not AUTOGEN_AVAILABLE:
                logger.warning("AutoGen not available, returning mock result")
                await asyncio.sleep(0.2)  # 模拟执行时间
                return {
                    "content": f"Mock team response for message: {message[:50]}...",
                    "team_type": type(team).__name__,
                    "mock": True,
                    "agents_involved": ["mock_agent_1", "mock_agent_2"]
                }

            # 使用AutoGen 0.6.2的正确API
            from autogen_core import CancellationToken

            # 创建取消令牌用于超时控制
            cancellation_token = CancellationToken()

            # 创建超时任务
            timeout_task = asyncio.create_task(asyncio.sleep(timeout))

            try:
                if stream:
                    # 使用流式执行
                    result = await self._execute_team_stream(team, message, cancellation_token)
                else:
                    # 使用标准执行
                    result = await self._execute_team_run(team, message, cancellation_token)

                # 取消超时任务
                timeout_task.cancel()
                return result

            except asyncio.TimeoutError:
                cancellation_token.cancel()
                raise ServiceError(f"Team execution timeout after {timeout}s")

        except Exception as e:
            logger.error(f"Team execution failed: {str(e)}")
            raise TeamError(f"Team execution failed: {str(e)}")

    async def _execute_team_run(self, team, message: str, cancellation_token) -> Any:
        """使用team.run()方法执行"""
        try:
            from autogen_agentchat.messages import TextMessage

            # 创建任务消息
            task_message = TextMessage(content=message, source="user")

            # 执行Team任务
            result = await team.run(task=task_message, cancellation_token=cancellation_token)

            # 提取结果
            if hasattr(result, 'messages') and result.messages:
                # 获取最后一条消息作为响应
                last_message = result.messages[-1]

                # 统计参与的Agent
                agents_involved = list(set([msg.source for msg in result.messages if hasattr(msg, 'source') and msg.source != 'user']))

                return {
                    "content": getattr(last_message, 'content', str(last_message)),
                    "source": getattr(last_message, 'source', 'team'),
                    "type": getattr(last_message, 'type', 'unknown'),
                    "stop_reason": getattr(result, 'stop_reason', None),
                    "message_count": len(result.messages),
                    "agents_involved": agents_involved,
                    "team_type": type(team).__name__
                }
            else:
                return {
                    "content": str(result),
                    "source": "team",
                    "type": "result",
                    "team_type": type(team).__name__
                }

        except Exception as e:
            logger.error(f"Team run execution failed: {str(e)}")
            raise

    async def _execute_team_stream(self, team, message: str, cancellation_token) -> Any:
        """使用team.run_stream()方法执行"""
        try:
            from autogen_agentchat.messages import TextMessage

            # 创建任务消息
            task_message = TextMessage(content=message, source="user")

            # 执行Team流式任务
            messages = []
            final_result = None
            agents_involved = set()

            async for item in team.run_stream(task=task_message, cancellation_token=cancellation_token):
                # 检查是否是TaskResult（最后一个项目）
                if hasattr(item, 'messages') and hasattr(item, 'stop_reason'):
                    final_result = item
                else:
                    # 收集中间消息
                    messages.append(item)
                    # 收集参与的Agent
                    if hasattr(item, 'source') and item.source != 'user':
                        agents_involved.add(item.source)

            # 返回结果
            if final_result:
                # 从最终结果中也收集Agent信息
                if hasattr(final_result, 'messages'):
                    for msg in final_result.messages:
                        if hasattr(msg, 'source') and msg.source != 'user':
                            agents_involved.add(msg.source)

                return {
                    "content": getattr(final_result.messages[-1], 'content', str(final_result.messages[-1])) if final_result.messages else "No response",
                    "source": getattr(final_result.messages[-1], 'source', 'team') if final_result.messages else 'team',
                    "type": "stream_result",
                    "stop_reason": getattr(final_result, 'stop_reason', None),
                    "message_count": len(final_result.messages),
                    "stream_messages": len(messages),
                    "agents_involved": list(agents_involved),
                    "team_type": type(team).__name__
                }
            else:
                return {
                    "content": "Stream completed without final result",
                    "source": "team",
                    "type": "stream_incomplete",
                    "stream_messages": len(messages),
                    "agents_involved": list(agents_involved),
                    "team_type": type(team).__name__
                }

        except Exception as e:
            logger.error(f"Team stream execution failed: {str(e)}")
            raise
