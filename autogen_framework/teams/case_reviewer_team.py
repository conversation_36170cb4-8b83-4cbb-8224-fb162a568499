from managers.team_manager import get_team_manager


def case_reviewer_team(agent_manager, termination_manager):
    """
    用例评审团队

    包含用例检索Agent和用例评审Agent，使用轮询方式进行协作

    Args:
        agent_manager: Agent管理器实例
        termination_manager: 终止条件管理器实例

    Returns:
        RoundRobinGroupChat团队实例
    """
    team_manager = get_team_manager()
    termination_condition = termination_manager.create_termination("max_message", max_messages=3)

    # 创建Agent实例而不是使用工厂函数
    case_retrieval_agent = agent_manager.create_agent('case_retrieval')
    case_reviewer_agent = agent_manager.create_agent('case_reviewer')

    team = team_manager.create_autogen_team(
        team_class='RoundRobinGroupChat',
        participants=[case_retrieval_agent, case_reviewer_agent],
        termination_condition=termination_condition
    )
    return team