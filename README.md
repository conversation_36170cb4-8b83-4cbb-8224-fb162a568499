# AutoGen Multi-Agent Framework

基于AutoGen和FastAPI的企业级多智能体系统框架。

## 项目概述

这是一个完整的多智能体系统框架，提供以下核心功能：

- **REST API接口**: 标准的HTTP API用于agent和team执行
- **动态Agent管理**: 通过代码定义agent，支持动态加载和创建
- **Team协作**: 支持多agent协作的team系统
- **模型管理**: 支持多种大语言模型的配置和切换
- **提示词管理**: 使用Markdown文件管理提示词模板
- **工具集成**: 支持HTTP和MCP工具接入

## 项目结构

```
autogen_framework/
├── main.py                 # FastAPI应用入口
├── routers/               # API路由层
├── services/              # 业务逻辑层
├── managers/              # 管理器层
├── agents/                # Agent定义
├── teams/                 # Team定义
├── prompts/               # 提示词模板
├── infrastructure/        # 基础设施层
├── config/                # 配置文件
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
cd autogen_framework
python main.py
```

### 3. 访问API文档

打开浏览器访问: http://localhost:8000/docs

## API接口

### Agent执行
```
POST /autogen/run/agent
{
    "name": "agent_name",
    "message": "user_message",
    "options": {
        "stream": False,
        "timeout": 30
    }
}
```

### Team执行
```
POST /autogen/run/team
{
    "name": "team_name",
    "message": "user_message",
    "options": {
        "stream": True,
        "timeout": 30
    }
}
```

## Agent开发

参考agents/case_reviewer_agent.py

## Team开发

参考teams/case_reviewer_team.py
